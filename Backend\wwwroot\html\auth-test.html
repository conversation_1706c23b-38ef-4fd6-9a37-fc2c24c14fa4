<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teams Authentication Test - AccureMD</title>
    <script src="https://res.cdn.office.net/teams-js/2.19.0/js/MicrosoftTeams.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        button {
            background: #6264A7;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5558a3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Teams Authentication Test</h1>
        
        <div class="test-section">
            <h3>Environment Detection</h3>
            <div id="environment-info"></div>
        </div>

        <div class="test-section">
            <h3>Teams Context</h3>
            <div id="teams-context"></div>
        </div>

        <div class="test-section">
            <h3>Authentication Test</h3>
            <button onclick="testAuthentication()">Test Teams Authentication</button>
            <button onclick="clearLogs()">Clear Logs</button>
            <div id="auth-result"></div>
        </div>

        <div class="test-section">
            <h3>Debug Logs</h3>
            <div id="debug-log" class="log"></div>
        </div>
    </div>

    <script>
        let logContainer = null;

        function log(message, type = 'info') {
            if (!logContainer) {
                logContainer = document.getElementById('debug-log');
            }
            
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLogs() {
            document.getElementById('debug-log').innerHTML = '';
        }

        // Environment detection
        function detectEnvironment() {
            const userAgent = navigator.userAgent;
            const isTeamsDesktop = userAgent.includes('Teams/') && !userAgent.includes('Chrome/');
            const isTeamsWeb = userAgent.includes('Chrome/') && window.parent !== window;
            const isStandalone = window.parent === window;

            const envInfo = document.getElementById('environment-info');
            envInfo.innerHTML = `
                <p><strong>User Agent:</strong> ${userAgent}</p>
                <p><strong>Teams Desktop App:</strong> <span class="${isTeamsDesktop ? 'success' : 'error'}">${isTeamsDesktop}</span></p>
                <p><strong>Teams Web:</strong> <span class="${isTeamsWeb ? 'success' : 'error'}">${isTeamsWeb}</span></p>
                <p><strong>Standalone Browser:</strong> <span class="${isStandalone ? 'success' : 'error'}">${isStandalone}</span></p>
                <p><strong>Window Parent:</strong> ${window.parent === window ? 'Same as window' : 'Different from window'}</p>
            `;

            log(`Environment detected - Desktop: ${isTeamsDesktop}, Web: ${isTeamsWeb}, Standalone: ${isStandalone}`);
        }

        // Teams context initialization
        async function initializeTeamsContext() {
            try {
                log('Initializing Teams SDK...');
                await microsoftTeams.app.initialize();
                log('Teams SDK initialized successfully', 'success');

                const context = await microsoftTeams.app.getContext();
                log('Teams context retrieved successfully', 'success');

                const contextInfo = document.getElementById('teams-context');
                contextInfo.innerHTML = `
                    <p><strong>User ID:</strong> ${context.user?.id || 'N/A'}</p>
                    <p><strong>User Name:</strong> ${context.user?.displayName || 'N/A'}</p>
                    <p><strong>Tenant ID:</strong> ${context.user?.tenant?.id || 'N/A'}</p>
                    <p><strong>Theme:</strong> ${context.app?.theme || 'N/A'}</p>
                    <p><strong>Locale:</strong> ${context.app?.locale || 'N/A'}</p>
                `;

                return context;
            } catch (error) {
                log(`Failed to initialize Teams context: ${error.message}`, 'error');
                document.getElementById('teams-context').innerHTML = `<p class="error">Failed to initialize Teams context: ${error.message}</p>`;
                return null;
            }
        }

        // Test authentication
        async function testAuthentication() {
            log('Starting authentication test...');
            
            const resultDiv = document.getElementById('auth-result');
            resultDiv.innerHTML = '<p class="info">Testing authentication...</p>';

            try {
                const context = await microsoftTeams.app.getContext();
                const userId = context?.user?.id || 'anonymous';
                const redirectUri = `${window.location.origin}/html/auth-callback.html`;
                const startLoginUrl = `${window.location.origin}/api/auth/start-teams-login?userId=${encodeURIComponent(userId)}&redirectUri=${encodeURIComponent(redirectUri)}`;

                log(`Authentication URL: ${startLoginUrl}`);
                log(`Redirect URI: ${redirectUri}`);
                log(`User ID: ${userId}`);

                // Add message listener for fallback communication
                const messageHandler = (event) => {
                    if (event.data && event.data.type === 'teams-auth-complete' && event.data.success) {
                        log('Received fallback auth success message', 'success');
                        window.removeEventListener('message', messageHandler);
                        resultDiv.innerHTML = '<p class="success">✅ Authentication successful (via fallback)!</p>';
                    }
                };
                window.addEventListener('message', messageHandler);

                microsoftTeams.authentication.authenticate({
                    url: startLoginUrl,
                    width: 600,
                    height: 700,
                    successCallback: (resultKey) => {
                        log(`Authentication succeeded with key: ${resultKey}`, 'success');
                        window.removeEventListener('message', messageHandler);
                        
                        const authData = localStorage.getItem(resultKey);
                        if (authData) {
                            log('Authentication data retrieved from localStorage', 'success');
                            resultDiv.innerHTML = '<p class="success">✅ Authentication successful!</p>';
                            localStorage.removeItem(resultKey);
                        } else {
                            log('No authentication data found in localStorage', 'error');
                            resultDiv.innerHTML = '<p class="error">❌ Authentication data not found</p>';
                        }
                    },
                    failureCallback: (reason) => {
                        log(`Authentication failed: ${reason}`, 'error');
                        window.removeEventListener('message', messageHandler);
                        
                        if (reason === 'CancelledByUser') {
                            log('CancelledByUser error detected - checking if this is the Teams desktop bug...', 'info');
                            resultDiv.innerHTML = '<p class="info">🔄 CancelledByUser detected - checking authentication status...</p>';
                            
                            // Wait and check if authentication actually succeeded
                            setTimeout(async () => {
                                try {
                                    const response = await fetch('/api/auth/status/me');
                                    if (response.ok) {
                                        const status = await response.json();
                                        if (status.isAuthenticated) {
                                            log('Authentication actually succeeded despite CancelledByUser error!', 'success');
                                            resultDiv.innerHTML = '<p class="success">✅ Authentication successful (Teams desktop bug workaround)!</p>';
                                        } else {
                                            log('Authentication genuinely failed', 'error');
                                            resultDiv.innerHTML = '<p class="error">❌ Authentication failed or was cancelled</p>';
                                        }
                                    } else {
                                        log('Could not check authentication status', 'error');
                                        resultDiv.innerHTML = '<p class="error">❌ Authentication failed or was cancelled</p>';
                                    }
                                } catch (error) {
                                    log(`Error checking auth status: ${error.message}`, 'error');
                                    resultDiv.innerHTML = '<p class="error">❌ Authentication failed or was cancelled</p>';
                                }
                            }, 2000);
                        } else {
                            resultDiv.innerHTML = `<p class="error">❌ Authentication failed: ${reason}</p>`;
                        }
                    }
                });

            } catch (error) {
                log(`Authentication test failed: ${error.message}`, 'error');
                resultDiv.innerHTML = `<p class="error">❌ Authentication test failed: ${error.message}</p>`;
            }
        }

        // Initialize on page load
        window.addEventListener('load', async () => {
            log('Page loaded, starting initialization...');
            detectEnvironment();
            await initializeTeamsContext();
            log('Initialization complete');
        });
    </script>
</body>
</html>
