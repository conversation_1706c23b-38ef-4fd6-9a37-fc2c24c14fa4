<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Callback - AccureMD</title>
    <script src="https://res.cdn.office.net/teams-js/2.19.0/js/MicrosoftTeams.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .callback-container {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 400px;
            width: 100%;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #6264A7;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .message {
            margin-top: 20px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="callback-container">
        <div class="spinner" id="spinner"></div>
        <h2>Processing Authentication...</h2>
        <div class="message" id="message">Please wait while we complete your sign-in.</div>
    </div>

    <script>
        console.log('AccureMD: Auth callback page loaded');

        // Helper function to parse URL hash parameters
        function getHashParameters() {
            const hash = window.location.hash.substring(1);
            const params = {};
            if (hash) {
                hash.split('&').forEach(param => {
                    const [key, value] = param.split('=');
                    if (key && value) {
                        params[decodeURIComponent(key)] = decodeURIComponent(value);
                    }
                });
            }
            return params;
        }

        // Helper function to parse URL query parameters
        function getQueryParameters() {
            const search = window.location.search.substring(1);
            const params = {};
            if (search) {
                search.split('&').forEach(param => {
                    const [key, value] = param.split('=');
                    if (key && value) {
                        params[decodeURIComponent(key)] = decodeURIComponent(value);
                    }
                });
            }
            return params;
        }
        console.log('AccureMD: Attempting to initialize Teams SDK in callback...');
        // Always try to initialize the SDK. Its success or failure is the most reliable
        // way to determine if we are in a Teams context.
        microsoftTeams.app.initialize().then(() => {
            // The SDK initialized, so we are definitely in Teams.
            console.log('AccureMD: Teams SDK initialized successfully. Using Teams context.');
            handleAuthenticationCallback(true); // Pass 'true' to use notifySuccess()
        }).catch((error) => {
            // The SDK failed to initialize, so we are in a standalone browser.
            console.error('AccureMD: Teams SDK failed to initialize, falling back to standalone browser mode.', error);
            handleAuthenticationCallback(false); // Pass 'false' for non-Teams behavior
         });
         
        async function handleAuthenticationCallback(useTeamsSDK) {
            // Parse both hash and query parameters from OAuth response
            const hashParams = getHashParameters();
            const queryParams = getQueryParameters();
            console.log('AccureMD: Hash parameters:', hashParams);
            console.log('AccureMD: Query parameters:', queryParams);

            // Combine parameters (hash takes precedence)
            const allParams = { ...queryParams, ...hashParams };

            // Check for error first
            if (allParams.error) {
                console.error('AccureMD: OAuth error:', allParams);
                document.getElementById('spinner').style.display = 'none';
                document.getElementById('message').innerHTML = `<span class="error">❌ Authentication failed: ${allParams.error_description || allParams.error}</span>`;
                localStorage.setItem('accuremd.auth.error', JSON.stringify(allParams));

                if (useTeamsSDK) {
                    try {
                        setTimeout(() => {
                            microsoftTeams.authentication.notifyFailure(allParams.error_description || allParams.error);
                        }, 100);
                    } catch (error) {
                        console.error('AccureMD: Error calling notifyFailure:', error);
                        // Fallback to non-Teams mode
                        if (window.opener && window.opener !== window) {
                            window.opener.postMessage({
                                type: 'auth-error',
                                error: allParams.error_description || allParams.error
                            }, window.location.origin);
                            setTimeout(() => {
                                window.close();
                            }, 2000);
                        }
                    }
                } else {
                    // Check if we're in a popup window
                    if (window.opener && window.opener !== window) {
                        // We're in a popup, send error message to parent
                        console.log('AccureMD: Sending error message to parent window');
                        window.opener.postMessage({
                            type: 'auth-error',
                            error: allParams.error_description || allParams.error
                        }, window.location.origin);

                        // Close the popup after a short delay
                        setTimeout(() => {
                            window.close();
                        }, 2000);
                    } else {
                        // Redirect back to main app with error
                        setTimeout(() => {
                            window.location.href = '/?error=' + encodeURIComponent(allParams.error_description || allParams.error);
                        }, 2000);
                    }
                }
                return;
            }

            // Check for access token or authorization code
            if (allParams.access_token || allParams.code) {
                console.log('AccureMD: Found authentication data - access_token:', !!allParams.access_token, 'code:', !!allParams.code);

                // Handle authorization code flow (exchange code for tokens)
                if (allParams.code && !allParams.access_token) {
                    console.log('AccureMD: Processing authorization code flow');
                    handleAuthorizationCode(allParams.code, allParams.state, useTeamsSDK);
                    return;
                }

                // Verify state parameter for direct token flow
                const expectedState = localStorage.getItem('accuremd.auth.state');
                if (expectedState && expectedState !== allParams.state) {
                    console.error('AccureMD: State mismatch - expected:', expectedState, 'got:', allParams.state);
                    document.getElementById('spinner').style.display = 'none';
                    document.getElementById('message').innerHTML = '<span class="error">❌ Authentication failed: Security validation failed</span>';
                    localStorage.setItem('accuremd.auth.error', JSON.stringify({
                        error: 'state_mismatch',
                        error_description: 'State parameter does not match'
                    }));

                    if (useTeamsSDK) {
                        try {
                            setTimeout(() => {
                                microsoftTeams.authentication.notifyFailure('StateDoesNotMatch');
                            }, 100);
                        } catch (error) {
                            console.error('AccureMD: Error calling notifyFailure for state mismatch:', error);
                            // Fallback to non-Teams mode
                            if (window.opener && window.opener !== window) {
                                window.opener.postMessage({
                                    type: 'auth-error',
                                    error: 'Security validation failed'
                                }, window.location.origin);
                                setTimeout(() => {
                                    window.close();
                                }, 2000);
                            }
                        }
                    } else {
                        // Check if we're in a popup window
                        if (window.opener && window.opener !== window) {
                            // We're in a popup, send error message to parent
                            window.opener.postMessage({
                                type: 'auth-error',
                                error: 'Security validation failed'
                            }, window.location.origin);

                            // Close the popup after a short delay
                            setTimeout(() => {
                                window.close();
                            }, 2000);
                        } else {
                            // Redirect back to main app with error
                            setTimeout(() => {
                                window.location.href = '/?error=' + encodeURIComponent('Security validation failed');
                            }, 2000);
                        }
                    }
                    return;
                }

                console.log('AccureMD: Authentication successful');
                document.getElementById('spinner').style.display = 'none';
                document.getElementById('message').innerHTML = '<span class="success">✅ Authentication successful! Establishing session...</span>';

                // Prepare result object
                const result = {
                    accessToken: allParams.access_token,
                    idToken: allParams.id_token,
                    tokenType: allParams.token_type,
                    expiresIn: allParams.expires_in,
                    scope: allParams.scope,
                    state: allParams.state,
                    code: allParams.code // Include authorization code if present
                };

                // Call backend API to establish session with the access token
                try {
                    console.log('AccureMD: Establishing session with backend...');
                    const sessionResponse = await fetch('/api/auth/establish-session', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            accessToken: result.accessToken,
                            idToken: result.idToken,
                            tokenType: result.tokenType,
                            expiresIn: result.expiresIn,
                            scope: result.scope,
                            state: result.state
                        })
                    });

                    if (sessionResponse.ok) {
                        const sessionResult = await sessionResponse.json();
                        console.log('AccureMD: Session established successfully:', sessionResult);
                        document.getElementById('message').innerHTML = '<span class="success">✅ Authentication successful! Redirecting...</span>';
                    } else {
                        console.warn('AccureMD: Failed to establish session, but continuing with client-side auth');
                    }
                } catch (sessionError) {
                    console.warn('AccureMD: Error establishing session:', sessionError);
                }

                // Store result in localStorage with a unique key
                const resultKey = 'accuremd.auth.result.' + Date.now();
                localStorage.setItem(resultKey, JSON.stringify(result));

                // Clean up state
                localStorage.removeItem('accuremd.auth.state');
                localStorage.removeItem('accuremd.auth.error');

                console.log('AccureMD: Processing authentication success');

                if (useTeamsSDK) {
                    // Notify Teams of success with the storage key
                    // Add a small delay to ensure everything is properly set up
                    console.log('AccureMD: Notifying Teams of authentication success with key:', resultKey);

                    try {
                        // Use a timeout to handle the Teams desktop app bug
                        setTimeout(() => {
                            microsoftTeams.authentication.notifySuccess(resultKey);
                        }, 100);
                    } catch (error) {
                        console.error('AccureMD: Error calling notifySuccess:', error);
                        // Fallback to non-Teams mode if notifySuccess fails
                        if (window.opener && window.opener !== window) {
                            window.opener.postMessage({
                                type: 'auth-success',
                                data: result
                            }, window.location.origin);
                            setTimeout(() => {
                                window.close();
                            }, 1000);
                        }
                    }
                } else {
                    // Check if we're in a popup window
                    if (window.opener && window.opener !== window) {
                        // We're in a popup, send message to parent
                        console.log('AccureMD: Sending success message to parent window');
                        window.opener.postMessage({
                            type: 'auth-success',
                            data: result
                        }, window.location.origin);

                        // Close the popup after a short delay
                        setTimeout(() => {
                            window.close();
                        }, 1000);
                    } else {
                        // Redirect back to main app with success
                        setTimeout(() => {
                            const returnUrl = localStorage.getItem('accuremd.auth.returnUrl') || '/';
                            localStorage.removeItem('accuremd.auth.returnUrl');
                            window.location.href = returnUrl + '?auth=success';
                        }, 1500);
                    }
                }

            } else {
                console.error('AccureMD: No access token found in callback');
                document.getElementById('spinner').style.display = 'none';
                document.getElementById('message').innerHTML = '<span class="error">❌ Authentication failed: No access token received</span>';
                localStorage.setItem('accuremd.auth.error', JSON.stringify({
                    error: 'no_token',
                    error_description: 'No access token received from OAuth provider'
                }));

                if (useTeamsSDK) {
                    try {
                        setTimeout(() => {
                            microsoftTeams.authentication.notifyFailure('UnexpectedFailure');
                        }, 100);
                    } catch (error) {
                        console.error('AccureMD: Error calling notifyFailure for unexpected failure:', error);
                        // Fallback to non-Teams mode
                        if (window.opener && window.opener !== window) {
                            window.opener.postMessage({
                                type: 'auth-error',
                                error: 'No access token received'
                            }, window.location.origin);
                            setTimeout(() => {
                                window.close();
                            }, 2000);
                        }
                    }
                } else {
                    // Check if we're in a popup window
                    if (window.opener && window.opener !== window) {
                        // We're in a popup, send error message to parent
                        window.opener.postMessage({
                            type: 'auth-error',
                            error: 'No access token received'
                        }, window.location.origin);

                        // Close the popup after a short delay
                        setTimeout(() => {
                            window.close();
                        }, 2000);
                    } else {
                        // Redirect back to main app with error
                        setTimeout(() => {
                            window.location.href = '/?error=' + encodeURIComponent('No access token received');
                        }, 2000);
                    }
                }
            }
        }

        // Handle authorization code flow
        async function handleAuthorizationCode(code, state, useTeamsSDK) {
            try {
                console.log('AccureMD: Exchanging authorization code for tokens');
                document.getElementById('message').innerHTML = '<span>🔄 Exchanging authorization code for tokens...</span>';

                const response = await fetch('/api/auth/callback', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        code: code,
                        state: state,
                        redirectUri: window.location.origin + '/html/auth-callback.html'
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                console.log('AccureMD: Token exchange successful:', result);

                if (result.success && result.user) {
                    document.getElementById('spinner').style.display = 'none';
                    document.getElementById('message').innerHTML = '<span class="success">✅ Authentication successful! Redirecting...</span>';

                    const authResult = {
                        accessToken: result.user.accessToken,
                        idToken: result.user.idToken,
                        tokenType: result.user.tokenType || 'Bearer',
                        expiresIn: result.user.expiresIn,
                        scope: result.user.scope,
                        userName: result.user.userName,
                        userId: result.user.userId,
                        email: result.user.email,
                        isAuthenticated: result.user.isAuthenticated
                    };

                    // Store result in localStorage
                    const resultKey = 'accuremd.auth.result.' + Date.now();
                    localStorage.setItem(resultKey, JSON.stringify(authResult));

                    // Clean up state
                    localStorage.removeItem('accuremd.auth.state');
                    localStorage.removeItem('accuremd.auth.error');

                    if (useTeamsSDK) {
                        // Notify Teams of success
                        console.log('AccureMD: Notifying Teams of authorization code success with key:', resultKey);

                        try {
                            // Use a timeout to handle the Teams desktop app bug
                            setTimeout(() => {
                                microsoftTeams.authentication.notifySuccess(resultKey);
                            }, 100);
                        } catch (error) {
                            console.error('AccureMD: Error calling notifySuccess in auth code flow:', error);
                            // Fallback to non-Teams mode if notifySuccess fails
                            if (window.opener && window.opener !== window) {
                                window.opener.postMessage({
                                    type: 'auth-success',
                                    data: authResult
                                }, window.location.origin);
                                setTimeout(() => {
                                    window.close();
                                }, 1000);
                            }
                        }
                    } else {
                        // Check if we're in a popup window
                        if (window.opener && window.opener !== window) {
                            // Send message to parent
                            window.opener.postMessage({
                                type: 'auth-success',
                                data: authResult
                            }, window.location.origin);

                            setTimeout(() => {
                                window.close();
                            }, 1000);
                        } else {
                            // Redirect back to main app
                            setTimeout(() => {
                                const returnUrl = localStorage.getItem('accuremd.auth.returnUrl') || '/';
                                localStorage.removeItem('accuremd.auth.returnUrl');
                                window.location.href = returnUrl + '?auth=success';
                            }, 1500);
                        }
                    }
                } else {
                    throw new Error(result.message || 'Token exchange failed');
                }

            } catch (error) {
                console.error('AccureMD: Authorization code exchange failed:', error);
                document.getElementById('spinner').style.display = 'none';
                document.getElementById('message').innerHTML = '<span class="error">❌ Authentication failed: ' + error.message + '</span>';

                if (useTeamsSDK) {
                    try {
                        setTimeout(() => {
                            microsoftTeams.authentication.notifyFailure(error.message);
                        }, 100);
                    } catch (notifyError) {
                        console.error('AccureMD: Error calling notifyFailure for auth code error:', notifyError);
                        // Fallback to non-Teams mode
                        if (window.opener && window.opener !== window) {
                            window.opener.postMessage({
                                type: 'auth-error',
                                error: error.message
                            }, window.location.origin);
                            setTimeout(() => {
                                window.close();
                            }, 2000);
                        }
                    }
                } else {
                    if (window.opener && window.opener !== window) {
                        window.opener.postMessage({
                            type: 'auth-error',
                            error: error.message
                        }, window.location.origin);

                        setTimeout(() => {
                            window.close();
                        }, 2000);
                    } else {
                        setTimeout(() => {
                            window.location.href = '/?error=' + encodeURIComponent(error.message);
                        }, 2000);
                    }
                }
            }
        }

        // Additional workaround for Teams desktop app issue
        // This handles the case where the Teams SDK doesn't properly communicate back to the parent
        window.addEventListener('beforeunload', function() {
            console.log('AccureMD: Window is about to unload, checking if we need to send final message');

            // If we're in a Teams context and haven't successfully notified, try one more time
            if (isInTeamsContext && window.parent && window.parent !== window) {
                try {
                    // Try to send a message to the parent window as a last resort
                    const authData = localStorage.getItem('accuremd.auth.result.' + Date.now());
                    if (authData) {
                        window.parent.postMessage({
                            type: 'teams-auth-complete',
                            success: true,
                            data: JSON.parse(authData)
                        }, '*');
                    }
                } catch (error) {
                    console.warn('AccureMD: Failed to send final auth message:', error);
                }
            }
        });

        // Also add a message listener for any additional communication
        window.addEventListener('message', function(event) {
            console.log('AccureMD: Received message in auth callback:', event.data);

            // Handle any additional messages if needed
            if (event.data && event.data.type === 'teams-auth-check') {
                // Respond with current auth status
                const authKeys = Object.keys(localStorage).filter(key => key.startsWith('accuremd.auth.result.'));
                if (authKeys.length > 0) {
                    const latestKey = authKeys[authKeys.length - 1];
                    const authData = localStorage.getItem(latestKey);
                    if (authData) {
                        event.source.postMessage({
                            type: 'teams-auth-status',
                            success: true,
                            key: latestKey,
                            data: JSON.parse(authData)
                        }, event.origin);
                    }
                }
            }
        });
    </script>
</body>
</html>
