{"GlobalPropertiesHash": "ITtAFkNiSgdhUb2BwCQUf6iyFsK/CwOgeB9JuCIv+uU=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["krYWps1CzkR5Ooh9BYy7m+6VhUh9fpaKyhRCEfueQzc=", "fF1YOyaP9eoSO8Ye2+d1lPZHy/UUr1CTF5rPDZlrHkY=", "4WV+ZFXdO3BJ364M0CWxeOArRuETGCnkAO87wyQaM6Q=", "2jlAfMeKZHTI2Gc6wHbI/9fwd5IM2YX/UhKrjfhLUqk=", "0DvZrFHfqgSlSTNIS5G9bmf4AdWKJurkDahoHvQXZGs=", "joYD/vl6iVdlybl1IRIIaUJXsRX8r/Qo3ILN9eHl7TI=", "5J3Hnw2IBK1LgYxmDgH9M/bsMHlJnoe1pQOlRI2dbcU=", "pwMNKHYoyazfe0KzN/TxmwShC8xSu4Nl3xIp1LRC7Cw=", "9pLLrzTrPJrsPZWKdVAmfryJUMVoCAoYUlM8PE3AfAA=", "IGMD/rISFgYifN398OCCzhNg2ogCKYe+Vs1jTd5tXzc=", "PfSy9iEmWCZmTlXtjhNtqpGcckvmuVG0YKHQNxHLUyE=", "qFlo+EWtmLm+4U7Gxv51oXPqVQeMlk06V4S/Hpw/l8A=", "jXe8u9EcawqGf88DGXkdzKhAToMOHUqGeLa++Kjewro=", "o8yfddgVdsjpuf1uhpjqDxApXitx0E/pex6zctd+AYA="], "CachedAssets": {"9pLLrzTrPJrsPZWKdVAmfryJUMVoCAoYUlM8PE3AfAA=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\teams-test.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "teams-test#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cztlu2xxj7", "Integrity": "+P5zgdWxWZ2bZZMeuIi7vJMm+530R8iVicKn/7LsAgo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\teams-test.html", "FileLength": 6390, "LastWriteTime": "2025-08-08T19:44:49.8475713+00:00"}, "fF1YOyaP9eoSO8Ye2+d1lPZHy/UUr1CTF5rPDZlrHkY=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-callback.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/auth-callback#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qg1k2229y6", "Integrity": "ai5vasQ3EEwxc/qLSYz67e7CWxPV6hmqX4oeGdnDzGE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\auth-callback.html", "FileLength": 19230, "LastWriteTime": "2025-08-08T21:57:39.6113503+00:00"}, "0DvZrFHfqgSlSTNIS5G9bmf4AdWKJurkDahoHvQXZGs=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\index.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h6nvgw2bxq", "Integrity": "GfcoGCssHEosZ5jXb6bdeIVquPUcwyOkuAeHCeKM3Hk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\index.html", "FileLength": 6691, "LastWriteTime": "2025-08-08T20:14:05.6425962+00:00"}, "2jlAfMeKZHTI2Gc6wHbI/9fwd5IM2YX/UhKrjfhLUqk=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\configure.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/configure#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inynrhuf2", "Integrity": "gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\configure.html", "FileLength": 4092, "LastWriteTime": "2025-08-08T17:45:39.2143566+00:00"}, "joYD/vl6iVdlybl1IRIIaUJXsRX8r/Qo3ILN9eHl7TI=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\privacy.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/privacy#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9ukuo7vfri", "Integrity": "AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\privacy.html", "FileLength": 1322, "LastWriteTime": "2025-08-08T08:30:26.6394667+00:00"}, "5J3Hnw2IBK1LgYxmDgH9M/bsMHlJnoe1pQOlRI2dbcU=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\termsofuse.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/termsofuse#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tv39flyyfq", "Integrity": "FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\termsofuse.html", "FileLength": 1168, "LastWriteTime": "2025-08-08T08:30:34.6547862+00:00"}, "krYWps1CzkR5Ooh9BYy7m+6VhUh9fpaKyhRCEfueQzc=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\css\\teams-app.css", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "css/teams-app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vwl5012ydz", "Integrity": "0b3Vytiq3sWD55HdmkfCBjn39BgFGuxMJFo7b1oziNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\teams-app.css", "FileLength": 11424, "LastWriteTime": "2025-08-08T19:01:32.3671149+00:00"}, "IGMD/rISFgYifN398OCCzhNg2ogCKYe+Vs1jTd5tXzc=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-auth.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "test-auth#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2r3ito90zx", "Integrity": "fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\test-auth.html", "FileLength": 2140, "LastWriteTime": "2025-08-08T11:50:16.6719653+00:00"}, "4WV+ZFXdO3BJ364M0CWxeOArRuETGCnkAO87wyQaM6Q=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-start.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/auth-start#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "uorbu8vuak", "Integrity": "7s42FjuMVMUg5i4L4V/DQ1ZnhSvl1nmF+vMTgUl89sk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\auth-start.html", "FileLength": 5567, "LastWriteTime": "2025-08-08T20:07:03.6376846+00:00"}, "pwMNKHYoyazfe0KzN/TxmwShC8xSu4Nl3xIp1LRC7Cw=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\js\\teams-app.js", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "js/teams-app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fwiwvcnhpb", "Integrity": "rwZjMRI/4g1r+0sjMh1oyjnZQ7Tn8oRjkWCp3h5AmSA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\teams-app.js", "FileLength": 17228, "LastWriteTime": "2025-08-08T21:56:39.4462626+00:00"}, "PfSy9iEmWCZmTlXtjhNtqpGcckvmuVG0YKHQNxHLUyE=": {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-teams-auth.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "test-teams-auth#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r54zswcdn2", "Integrity": "zxsIQCwlwqYJYejoAnllGdxDbA0/p/akEX/n4Ydb17I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\test-teams-auth.html", "FileLength": 9705, "LastWriteTime": "2025-08-08T20:45:49.2592213+00:00"}}, "CachedCopyCandidates": {}}